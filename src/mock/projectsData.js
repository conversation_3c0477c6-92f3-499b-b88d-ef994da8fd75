const projectsData = [
  {
    id: 1,
    title: "Academia For IT - Agenda Simple",
    description: "Aplicación web de agenda simple y funcional desarrollada con tecnologías modernas. Permite gestionar tareas y eventos de manera intuitiva.",
    technologies: ["React", "JavaScript", "CSS", "Vercel"],
    image: "https://i.imgur.com/h5EKdcH.png",
    github: "igna<PERSON>-<PERSON>/academiaForIT",
    url: "https://github.com/igna<PERSON>-<PERSON>/academiaForIT",
    web: "https://academia-for-it.vercel.app/"
  },
  {
    id: 2,
    title: "El Rincón del Vino (E-commerce)",
    description: "Proyecto Final Laboratorio 4 - Desarrollo de una aplicación web full-stack para ventas de vinos.",
    technologies: ["React", "Node.js", "MongoDB"],
    image: "https://github.com/CodeStrong2023/404notFound-4sTP/raw/main/client/assets/navegaci%C3%B3n.png",
    github: "CodeStrong2023/404notFound-4sTP",
    url: "https://github.com/CodeStrong2023/404notFound-4sTP"
  },
  {
    id: 3,
    title: "Sitio Web Colegio La Rioja",
    description: "Proyecto de Pasantía - Desarrollo de un sitio web funcional para una institución educativa.",
    technologies: ["HTML", "CSS", "JavaScript"],
    image: "https://i.imgur.com/5HXiadn.png",
    github: "DiegoRafaelRamosLlanos/tecnicoIndd/tree/ignacio",
    url: "https://github.com/DiegoRafaelRamosLlanos/tecnicoIndd/tree/ignacio"
  },
  {
    id: 4,
    title: "Sistema de Gestión de Reclamos",
    description: "Sistema web para registrar y resolver reclamos.",
    technologies: ["React", "Express", "MySQL"],
    image: "https://via.placeholder.com/400x250?text=Proyecto+No+Disponible",
    github: "ignacio-Jose-Rocha/programacion3",
    url: "https://github.com/ignacio-Jose-Rocha/programacion3"
  },
  {
    id: 5,
    title: "Chat Bot UTN",
    description: "Bot creado para la UTN con HTML, CSS y JavaScript.",
    technologies: ["HTML", "CSS", "JavaScript"],
    image: "https://i.imgur.com/0v9Bebt.png",
    github: "ignacio-Jose-Rocha/chatBotEnvioMailUTN",
    url: "https://github.com/ignacio-Jose-Rocha/chatBotEnvioMailUTN"
  },
  {
    id: 6,
    title: "Visión Películas",
    description: "Proyecto para Alkemy usando React - Plataforma de películas.",
    technologies: ["React", "API Rest", "TailwindCSS"],
    image: "https://i.imgur.com/y1C4ZuP.png",
    github: "ignacio-Jose-Rocha/vision-peliculas",
    url: "https://github.com/ignacio-Jose-Rocha/vision-peliculas"
  },
  {
    id: 7,
    title: "API Rick y Morty",
    description: "Página hecha en React sobre la serie Rick y Morty consumiendo API.",
    technologies: ["React", "API", "CSS Modules"],
    image: "https://i.imgur.com/xFyb63k.png",
    github: "ignacio-Jose-Rocha/api-rick-morty",
    url: "https://github.com/ignacio-Jose-Rocha/api-rick-morty"
  },
  {
    id: 8,
    title: "Gimnasio Center Fit",
    description: "Sitio web full-stack para gimnasio con sistema de reservas.",
    technologies: ["React", "Node.js", "MongoDB"],
    image: "https://i.imgur.com/5Lbz8Od.png",
    github: "ignacio-Jose-Rocha/gimFront",
    url: "https://github.com/ignacio-Jose-Rocha/gimFront"
  },
  {
    id: 9,
    title: "Clinica medicus",
    description: "Sitio web full-stack para clinica medicus.",
    technologies: ["React", "Node.js", "PostgreSQL", "JWT", "Nodemailer"],
    image: "https://i.imgur.com/Sn3Hk6Q.png",
    github: "ignacio-Jose-Rocha/medicus",
    url: "https://github.com/ignacio-Jose-Rocha/medicus",
    web: "https://medicus-clinica.vercel.app/login"
  }

];

export default projectsData;

