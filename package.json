{"scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "dependencies": {"react": "^18.0.0", "react-dom": "^18.0.0", "react-scripts": "^5.0.0", "postcss": "^8", "tailwindcss": "^3.4.1", "autoprefixer": "^10.0.0", "tailwind-merge": "^2.4.0", "tailwindcss-animate": "^1.0.7"}, "main": "/src/index.js", "devDependencies": {}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}